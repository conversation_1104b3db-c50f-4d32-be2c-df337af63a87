use crate::repository::user::user_repository::UserRepository;
use crate::utils::password::PasswordService;
use anyhow::{bail, Result};
use chrono::{DateTime, Utc};
use regex::Regex;
use serde::{Deserialize, Serialize};
use sqlx::{PgPool, Row};
use std::sync::Arc;
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserInfo {
    pub user_id: Uuid,
    pub username: String,
    pub roles: Vec<String>,
    pub is_admin: bool,
    pub is_teacher: bool,
    pub is_student: bool,

    pub phone_number: String,
    pub created_at: Option<DateTime<Utc>>,
    pub phone_verified: Option<bool>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoleAssignmentRequest {
    pub user_id: Uuid,
    pub role_type: String,
    pub tenant_id: Option<Uuid>,
}

// 分页查询参数
#[derive(Debug, Deserialize, Clone)]
pub struct GetUsersQuery {
    pub page: Option<u32>,      // 页码，从1开始
    pub per_page: Option<u32>,  // 每页数量，默认20
    pub search: Option<String>, // 搜索关键词
    pub role_filter: Option<String>, // 角色过滤
    pub status_filter: Option<String>, // 状态过滤
}

// 分页元数据
#[derive(Debug, Serialize)]
pub struct PaginationMeta {
    pub current_page: u32,
    pub per_page: u32,
    pub total: i64,
    pub total_pages: u32,
    pub has_next: bool,
    pub has_prev: bool,
}

// 分页响应结构
#[derive(Debug, Serialize)]
pub struct PaginatedUsersResponse {
    pub users: Vec<UserInfo>,
    pub pagination: PaginationMeta,
}

// 请求数据结构
#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub phone: String,
    pub password: String,
}

// 响应数据结构
#[derive(Serialize, Debug)]
pub struct UserResponse {
    pub user_id: Uuid,
    pub username: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct UpdateUserRequest {
    pub user_id: Uuid,            // 要修改的用户 ID
    pub password: Option<String>, // 修改密码
    pub phone: Option<String>,    // 修改手机号
    pub is_active: Option<bool>,  // 激活状态
}

// ===== User Service =====

#[derive(Clone)]
pub struct UserService {
    pool: PgPool,
    password_service: Arc<PasswordService>,
}

impl UserService {
    pub fn new(pool: PgPool, password_service: Arc<PasswordService>) -> Self {
        Self {
            pool,
            password_service,
        }
    }

    /// 获取所有用户列表 (PRD 6.3.2 compliant)
    pub async fn get_all_users(&self) -> Result<Vec<UserInfo>> {
        // 查询所有用户
        let user_records = UserRepository::fetch_all_users(&self.pool).await?;

        // 获取所有活跃租户的schema
        let tenant_schemas = UserRepository::fetch_active_tenant_schemas(&self.pool).await?;

        let mut users = Vec::new();

        for user_record in user_records {
            let user_id: Uuid = user_record.get("id");
            let username: String = user_record.get("username");
            let phone_number: String = user_record.get("phone_number");
            let created_at: Option<DateTime<Utc>> = user_record.get("created_at");
            let phone_verified: Option<bool> = user_record.get("phone_verified");
            let is_active: Option<bool> = user_record.get("is_active");

            let mut roles = Vec::new();

            // 查询用户在所有租户中的角色
            for schema_name in &tenant_schemas {
                let tenant_roles = UserRepository::fetch_user_roles_in_tenant(&self.pool, user_id, schema_name).await?;
                for role_code in tenant_roles {
                    if !roles.contains(&role_code) {
                        roles.push(role_code);
                    }
                }
            }

            // Check for system admin (special case)
            if username == "admin" {
                roles.push("admin".to_string());
            }

            let user_info = UserInfo {
                user_id,
                username: username.clone(),
                phone_number: phone_number.clone(),
                created_at,
                phone_verified: phone_verified.clone(),
                is_active: is_active.clone(),
                is_admin: roles.contains(&"admin".to_string()),
                is_teacher: roles.contains(&"teacher".to_string()),
                is_student: roles.contains(&"student".to_string()),
                roles,
            };

            users.push(user_info);
        }

        Ok(users)
    }

    /// 获取用户列表（支持分页和筛选）
    pub async fn get_users_paginated(&self, query: GetUsersQuery) -> Result<PaginatedUsersResponse> {
        let page = query.page.unwrap_or(1).max(1);
        let per_page = query.per_page.unwrap_or(20).min(100); // 限制最大每页数量为100

        // 使用 repository 获取分页数据
        let (user_records, total) = UserRepository::fetch_users_paginated(&self.pool, &query).await?;

        // 获取所有活跃租户的schema
        let tenant_schemas = UserRepository::fetch_active_tenant_schemas(&self.pool).await?;

        let mut users = Vec::new();

        for user_record in user_records {
            let user_id: Uuid = user_record.get("id");
            let username: String = user_record.get("username");
            let phone_number: String = user_record.get("phone_number");
            let created_at: Option<DateTime<Utc>> = user_record.get("created_at");
            let phone_verified: Option<bool> = user_record.get("phone_verified");
            let is_active: Option<bool> = user_record.get("is_active");

            let mut roles = Vec::new();

            // 查询用户在所有租户中的角色
            for schema_name in &tenant_schemas {
                let tenant_roles = UserRepository::fetch_user_roles_in_tenant(&self.pool, user_id, schema_name).await?;
                for role_code in tenant_roles {
                    if !roles.contains(&role_code) {
                        roles.push(role_code);
                    }
                }
            }

            // Check for system admin (special case)
            if username == "admin" {
                roles.push("admin".to_string());
            }

            let user_info = UserInfo {
                user_id,
                username: username.clone(),
                phone_number: phone_number.clone(),
                created_at,
                phone_verified: phone_verified.clone(),
                is_active: is_active.clone(),
                is_admin: roles.contains(&"admin".to_string()),
                is_teacher: roles.contains(&"teacher".to_string()),
                is_student: roles.contains(&"student".to_string()),
                roles,
            };

            // 客户端角色过滤（因为角色信息需要从多个schema查询）
            if let Some(role_filter) = &query.role_filter {
                if role_filter != "all" {
                    let matches_filter = match role_filter.as_str() {
                        "admin" => user_info.is_admin,
                        "teacher" => user_info.is_teacher,
                        "student" => user_info.is_student,
                        _ => true,
                    };
                    if !matches_filter {
                        continue;
                    }
                }
            }

            // 客户端状态过滤
            if let Some(status_filter) = &query.status_filter {
                if status_filter != "all" {
                    let matches_filter = match status_filter.as_str() {
                        "active" => user_info.is_active.unwrap_or(false),
                        "inactive" => !user_info.is_active.unwrap_or(true),
                        "verified" => user_info.phone_verified.unwrap_or(false),
                        "unverified" => !user_info.phone_verified.unwrap_or(true),
                        _ => true,
                    };
                    if !matches_filter {
                        continue;
                    }
                }
            }

            users.push(user_info);
        }

        // 计算分页信息
        let total_pages = ((total as f64) / (per_page as f64)).ceil() as u32;
        let pagination = PaginationMeta {
            current_page: page,
            per_page,
            total,
            total_pages,
            has_next: page < total_pages,
            has_prev: page > 1,
        };

        Ok(PaginatedUsersResponse { users, pagination })
    }

    /// 检查用户是否存在
    pub async fn user_exists(&self, user_id: Uuid) -> Result<bool> {
        UserRepository::user_exists(&self.pool, user_id).await
    }

    /// 获取用户角色 (PRD 6.3.2 compliant - queries across all tenant schemas)
    pub async fn get_user_roles(&self, user_id: Uuid) -> Result<Vec<String>> {
        let mut roles = Vec::new();

        // 获取所有活跃租户的schema
        let tenant_schemas = UserRepository::fetch_active_tenant_schemas(&self.pool).await?;

        // 查询用户在所有租户中的角色
        for schema_name in &tenant_schemas {
            let tenant_roles = UserRepository::fetch_user_roles_in_tenant(&self.pool, user_id, schema_name).await?;
            for role_code in tenant_roles {
                if !roles.contains(&role_code) {
                    roles.push(role_code);
                }
            }
        }

        // Check for system admin (special case)
        if let Some((username, phone_number)) = UserRepository::fetch_user_basic_info(&self.pool, user_id).await? {
            if username == "admin" || phone_number == "admin" {
                roles.push("admin".to_string());
            }
        }

        Ok(roles)
    }

    pub async fn create_user(
        &self,
        create_user_request: CreateUserRequest,
    ) -> Result<UserResponse> {
        // 1. 输入验证
        self.validate_register(&create_user_request)?;

        // 2. 唯一性检查（伪代码，实际需要数据库）
        self.check_exists(&create_user_request).await?;

        // 3. 密码加盐哈希（伪代码，实际需要实现）
        let (password_hash, salt) = self
            .password_service
            .hash_password(&create_user_request.password)?;

        // 4. 插入数据库，返回 id 和 created_at
        let (user_id, created_at, username) = self
            .insert_new_user(&create_user_request, &password_hash, &salt)
            .await?;

        // 6.返回
        Ok(UserResponse {
            user_id,
            username,
            created_at,
        })
    }

    pub fn validate_register(&self, create_user_request: &CreateUserRequest) -> Result<()> {
        let CreateUserRequest {
            username,
            password,
            phone,
            ..
        } = create_user_request;

        self.validate_username(username)?;
        self.validate_password(password)?;
        self.validate_phone(phone)?;

        Ok(())
    }

    pub fn validate_username(&self, username: &str) -> Result<()> {
        // 用户名验证：3-50字符，只允许字母数字
        let username_len = username.len();
        if username_len < 3 || username_len > 50 {
            bail!("用户名长度必须在3-50字符之间");
        }
        let re = Regex::new(r"^[a-zA-Z0-9]+$").unwrap();
        if !re.is_match(username) {
            bail!("用户名只能包含大小写字母和数字");
        }
        Ok(())
    }

    pub fn validate_password(&self, password: &str) -> Result<()> {
        // 密码验证：6-64字符，仅允许小写字母、大写字母、数字、标点符号
        let password_len = password.len();
        if password_len < 6 || password_len > 64 {
            bail!("密码长度必须在6-64字符之间");
        }

        // 检查密码是否包含仅允许的字符类型
        if !password.chars().all(|ch| {
            ch.is_ascii_lowercase()
                || ch.is_ascii_uppercase()
                || ch.is_ascii_digit()
                || ch.is_ascii_punctuation()
        }) {
            bail!("密码只能包含：小写字母、大写字母、数字、标点符号");
        }
        Ok(())
    }

    pub fn validate_phone(&self, phone: &str) -> Result<()> {
        // 手机号验证：11位纯数字
        if phone.len() != 11 {
            bail!("手机号长度需为11位");
        }
        if !phone.chars().all(|c| c.is_ascii_digit()) {
            bail!("手机号只能包含数字");
        }
        Ok(())
    }

    pub async fn check_exists(&self, create_user_request: &CreateUserRequest) -> Result<()> {
        let CreateUserRequest {
            username, phone, ..
        } = create_user_request;

        self.check_username_exists(username).await?;
        self.check_phone_exists(phone).await?;

        Ok(())
    }

    pub async fn check_username_exists(&self, username: &str) -> Result<()> {
        let exists = UserRepository::username_exists(&self.pool, username).await?;

        if exists {
            bail!("用户名已被占用");
        } else {
            Ok(())
        }
    }

    pub async fn check_phone_exists(&self, phone: &str) -> Result<()> {
        let exists = UserRepository::phone_exists(&self.pool, phone).await?;

        if exists {
            bail!("手机号已被占用");
        } else {
            Ok(())
        }
    }

    pub async fn check_phone_with_id_exists(&self, phone: &str, id: Uuid) -> Result<()> {
        let exists = UserRepository::phone_exists_exclude_user(&self.pool, phone, id).await?;

        if exists {
            bail!("手机号已被占用");
        } else {
            Ok(())
        }
    }

    pub async fn insert_new_user(
        &self,
        request: &CreateUserRequest,
        password_hash: &str,
        salt: &str,
    ) -> Result<(Uuid, DateTime<Utc>, String)> {
        UserRepository::insert_user(
            &self.pool,
            &request.username,
            &request.phone,
            password_hash,
            salt,
        ).await
    }

    /// 根据手机号查找用户
    pub async fn get_user_by_phone(&self, phone: &str) -> Result<Option<UserResponse>> {
        let user_record = sqlx::query!(
            "SELECT id, username, created_at FROM public.users WHERE phone_number = $1 AND is_active = true",
            phone
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(record) = user_record {
            Ok(Some(UserResponse {
                user_id: record.id,
                username: record.username,
                created_at: record.created_at.unwrap_or_else(|| chrono::Utc::now()),
            }))
        } else {
            Ok(None)
        }
    }

    /// 根据用户名查找用户
    pub async fn get_user_by_username(&self, username: &str) -> Result<Option<UserResponse>> {
        let user_record = sqlx::query!(
            "SELECT id, username, created_at FROM public.users WHERE username = $1 AND is_active = true",
            username
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(record) = user_record {
            Ok(Some(UserResponse {
                user_id: record.id,
                username: record.username,
                created_at: record.created_at.unwrap_or_else(|| chrono::Utc::now()),
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_user(&self, req: UpdateUserRequest) -> Result<()> {
        // 检查用户是否存在且获取admin状态
        let (user_exists, is_admin) = UserRepository::check_user_exists_and_admin_status(&self.pool, req.user_id).await?;

        if !user_exists {
            bail!("用户不存在");
        }

        if let Some(true) = is_admin {
            bail!("不允许操作 admin 用户");
        }

        // 验证输入并准备更新数据
        let mut phone_to_update: Option<&str> = None;
        let mut password_hash_to_update: Option<String> = None;
        let mut salt_to_update: Option<String> = None;
        let mut has_updates = false;

        // 验证并准备手机号更新
        if let Some(phone) = &req.phone {
            self.validate_phone(phone)?;
            self.check_phone_with_id_exists(phone, req.user_id).await?;
            phone_to_update = Some(phone);
            has_updates = true;
        }

        // 验证并准备密码更新
        if let Some(password) = &req.password {
            self.validate_password(password)?;
            let (password_hash, salt) = self.password_service.hash_password(password)?;
            password_hash_to_update = Some(password_hash);
            salt_to_update = Some(salt);
            has_updates = true;
        }

        // 检查是否有激活状态更新
        if req.is_active.is_some() {
            has_updates = true;
        }

        if !has_updates {
            bail!("没有需要更新的字段");
        }

        // 使用 repository 进行更新
        UserRepository::update_user_fields(
            &self.pool,
            req.user_id,
            phone_to_update,
            password_hash_to_update.as_deref(),
            salt_to_update.as_deref(),
            req.is_active,
        ).await?;

        Ok(())
    }
}
