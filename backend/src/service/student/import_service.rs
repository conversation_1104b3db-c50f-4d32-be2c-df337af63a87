use chrono::{NaiveDate, Utc};
use serde_json;
use sqlx::{PgConnection, PgPool};
use std::sync::Arc;
use tracing::{error, info};
use uuid::Uuid;

use crate::controller::student::student_controller::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use crate::model::{CreateStudentParams, Student};
use crate::service::administrative_classes::administrative_classes_service::AdministrativeClassesService;
use crate::service::role::role_service::RoleService;
use crate::service::student::student_service::StudentService;
use crate::service::tenant::tenant_service::TenantService;
use crate::service::tenant::user::tenant_user_service::TenantUserService;
use crate::service::user::user_service::{UserService, CreateUserRequest};
use crate::utils::error::AppError;
use crate::utils::schema::{connect_with_schema, validate_schema_name};
use crate::controller::tenant::user::tenant_user_controller::AddUserToTenantRequest;
use crate::model::role::role::AssignRoleRequest;

/// 学生导入记录
#[derive(Debug, Clone)]
pub struct StudentImportRecord {
    pub student_number: String,
    pub student_name: String,
    pub class_code: Option<String>,
    pub grade_name: Option<String>,
    pub birth_date: Option<NaiveDate>,
    pub id_number: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub guardian_name: Option<String>,
    pub guardian_phone: Option<String>,
    pub guardian_relation: Option<String>,
}

/// 学生导入服务
#[derive(Clone)]
pub struct StudentImportService {
    db_pool: PgPool,
    student_service: Arc<StudentService>,
    user_service: UserService,
    tenant_user_service: Arc<TenantUserService>,
    tenant_service: TenantService,
    role_service: RoleService,
    administrative_classes_service: Arc<AdministrativeClassesService>,
}

impl StudentImportService {
    pub fn new(
        db_pool: PgPool,
        student_service: Arc<StudentService>,
        user_service: UserService,
        tenant_user_service: Arc<TenantUserService>,
        tenant_service: TenantService,
        role_service: RoleService,
        administrative_classes_service: Arc<AdministrativeClassesService>,
    ) -> Self {
        Self {
            db_pool,
            student_service,
            user_service,
            tenant_user_service,
            tenant_service,
            role_service,
            administrative_classes_service,
        }
    }

    /// 学生批量导入功能
    pub async fn import_students(
        &self,
        schema_name: &str,
        data: &[u8],
        user_id: Uuid,
    ) -> Result<ImportResult, String> {
        use calamine::{Reader, Xlsx, open_workbook_from_rs};
        use std::io::Cursor;

        let cursor = Cursor::new(data);
        let mut workbook: Xlsx<_> = open_workbook_from_rs(cursor)
            .map_err(|e| format!("无法打开Excel文件: {}", e))?;

        let sheet_names = workbook.sheet_names().to_owned();
        if sheet_names.is_empty() {
            return Err("Excel文件中没有工作表".to_string());
        }

        let mut total_success = 0;
        let mut total_failed = 0;
        let mut total_errors = Vec::new();
        let mut sheets_processed = Vec::new();

        // 遍历所有工作表
        for sheet_name in &sheet_names {
            info!("Processing sheet: {}", sheet_name);
            
            let range = match workbook.worksheet_range(sheet_name) {
                Ok(range) => range,
                Err(e) => {
                    let sheet_error = ImportError {
                        row: 0,
                        error: format!("读取工作表 '{}' 失败: {}", sheet_name, e),
                        data: serde_json::Value::Null,
                    };
                    total_errors.push(sheet_error.clone());
                    total_failed += 1;
                    
                    sheets_processed.push(SheetResult {
                        sheet_name: sheet_name.clone(),
                        success: 0,
                        failed: 1,
                        errors: vec![sheet_error],
                    });
                    continue;
                }
            };

            let mut sheet_success = 0;
            let mut sheet_failed = 0;
            let mut sheet_errors = Vec::new();

            // 跳过标题行，从第二行开始处理
            for (row_num, row) in range.rows().enumerate().skip(1) {
                match self.parse_student_row(row, row_num + 2) {
                    Ok(record) => {
                        match self.process_student_import_record(schema_name, &record, user_id).await {
                            Ok(_) => {
                                sheet_success += 1;
                                info!("Successfully imported student: {} from sheet: {}", record.student_name, sheet_name);
                            }
                            Err(e) => {
                                sheet_failed += 1;
                                let import_error = ImportError {
                                    row: (row_num + 2) as i32,
                                    error: format!("导入学生失败: {}", e),
                                    data: serde_json::to_value(&record).unwrap_or(serde_json::Value::Null),
                                };
                                sheet_errors.push(import_error.clone());
                                error!("Failed to import student {} from sheet {}: {}", record.student_name, sheet_name, e);
                            }
                        }
                    }
                    Err(e) => {
                        sheet_failed += 1;
                        let import_error = ImportError {
                            row: (row_num + 2) as i32,
                            error: e,
                            data: serde_json::Value::Null,
                        };
                        sheet_errors.push(import_error.clone());
                    }
                }
            }
            
            // 记录当前工作表的处理结果
            total_success += sheet_success;
            total_failed += sheet_failed;
            total_errors.extend(sheet_errors.clone());
            
            sheets_processed.push(SheetResult {
                sheet_name: sheet_name.clone(),
                success: sheet_success,
                failed: sheet_failed,
                errors: sheet_errors,
            });
            
            info!("Completed processing sheet '{}': {} success, {} failed", sheet_name, sheet_success, sheet_failed);
        }

        info!("Completed processing all sheets: {} total success, {} total failed", total_success, total_failed);

        Ok(ImportResult {
            success: total_success,
            failed: total_failed,
            errors: total_errors,
            sheets_processed,
        })
    }

    /// 处理单个学生导入记录
    async fn process_student_import_record(
        &self,
        schema_name: &str,
        record: &StudentImportRecord,
        _user_id: Uuid,
    ) -> Result<Student, String> {
        let safe_schema = validate_schema_name(schema_name)
            .map_err(|e| format!("无效的schema名称: {}", e))?;

        // 1. 生成用户名：学号@schema_name(去掉tenant_前缀)
        let username = self.generate_username(&record.student_number, &safe_schema);
        
        // 2. 创建或获取用户
        let user_response = self.create_or_get_user(&username, &record.student_number).await?;
        
        // 3. 获取租户信息
        let tenant_response = self.tenant_service.get_tenant_by_schema(&safe_schema).await
            .map_err(|e| format!("获取租户信息失败: {}", e))?;

        // 4. 关联用户到租户成员
        let add_user_request = AddUserToTenantRequest {
            user_id: user_response.user_id,
            access_type: Some("member".to_string()),
            expires_at: None,
        };

        self.tenant_user_service.add_user_to_tenant(tenant_response.id, add_user_request).await
            .map_err(|e| format!("添加用户到租户失败: {}", e))?;

        // 5. 分配学生角色（不写入target_type和target_id）
        self.assign_student_role(user_response.user_id, tenant_response.id, &safe_schema).await?;

        // 6. 处理行政班级
        let administrative_class_id = if let Some(ref class_code) = record.class_code {
            Some(self.handle_administrative_class(&safe_schema, class_code, record.grade_name.as_deref()).await?)
        } else {
            None
        };

        // 7. 在当前租户下根据学生学号查找，如果存在则返回，不存在才创建
        let student = self.create_or_get_student(&safe_schema, record, user_response.user_id, administrative_class_id).await?;

        Ok(student)
    }

    /// 生成用户名：学号@schema_name(去掉tenant_前缀)
    fn generate_username(&self, student_number: &str, schema_name: &str) -> String {
        let schema_suffix = if schema_name.starts_with("tenant_") {
            &schema_name[7..] // 去掉"tenant_"前缀
        } else {
            schema_name
        };
        format!("{}@{}", student_number, schema_suffix)
    }

    /// 创建或获取用户
    async fn create_or_get_user(&self, username: &str, student_number: &str) -> Result<crate::service::user::user_service::UserResponse, String> {
        // 按用户名查找，存在则返回，不存在则创建
        match self.user_service.get_user_by_username(username).await {
            Ok(Some(existing_user)) => {
                info!("Found existing user: {}", username);
                Ok(existing_user)
            }
            Ok(None) => {
                // 用户不存在，创建新用户
                let user_create_request = CreateUserRequest {
                    username: username.to_string(),
                    phone: student_number.to_string(), // 使用学号作为手机号的占位符
                    password: "123456".to_string(), // 默认密码
                };

                self.user_service.create_user(user_create_request).await
                    .map_err(|e| format!("创建用户失败: {}", e))
            }
            Err(e) => Err(format!("查询用户失败: {}", e))
        }
    }

    /// 分配学生角色
    async fn assign_student_role(&self, user_id: Uuid, tenant_id: Uuid, schema_name: &str) -> Result<(), String> {
        // 获取学生角色
        match self.role_service.get_role_by_code("student").await {
            Ok(role_vo) => {
                let assign_request = AssignRoleRequest {
                    user_id,
                    tenant_id,
                    role_id: role_vo.id,
                    target_type: None, // 根据要求，不写入target_type
                    target_id: None,   // 根据要求，不写入target_id
                    subject: None,
                    display_name: None,
                };

                self.role_service.assign_role(assign_request, schema_name).await
                    .map_err(|e| format!("分配学生角色失败: {}", e))?;

                Ok(())
            }
            Err(e) => Err(format!("获取学生角色失败: {}", e))
        }
    }

    /// 处理行政班级：根据班级代码创建或查找班级
    async fn handle_administrative_class(
        &self,
        schema_name: &str,
        class_code: &str,
        grade_name: Option<&str>,
    ) -> Result<Uuid, String> {
        use crate::model::administrative_classes::administrative_classes::CreateAdministrativeClassesParams;

        info!("查找班级代码: {} 在租户: {}", class_code, schema_name);

        // 先查找是否已存在该班级代码
        match self.administrative_classes_service.find_all_by_code(&schema_name.to_string(), &class_code.to_string()).await {
            Ok(classes) if !classes.is_empty() => {
                // 班级已存在，返回第一个匹配的班级ID
                info!("找到已存在的班级: {} (ID: {})", classes[0].class_name, classes[0].id);
                Ok(classes[0].id)
            }
            Ok(_) => {
                info!("班级代码 {} 不存在，需要创建新班级", class_code);
                // 班级不存在，需要创建
                let class_name = self.generate_class_name(class_code)?;

                // 根据年级名称查找年级代码
                let grade_level_code = if let Some(grade) = grade_name {
                    self.map_grade_name_to_code(grade)
                } else {
                    None
                };

                let create_params = CreateAdministrativeClassesParams {
                    class_name: class_name.clone(),
                    code: Some(class_code.to_string()),
                    academic_year: Some("2024-2025".to_string()), // 默认学年
                    grade_level_code,
                    teacher_id: None, // 暂时不指定班主任
                };

                info!("尝试创建班级: {} (代码: {})", class_name, class_code);
                match self.administrative_classes_service.create_classes(&schema_name.to_string(), &Uuid::new_v4(), &create_params).await {
                    Ok(class) => {
                        info!("成功创建班级: {} ({})", class.class_name, class_code);
                        Ok(class.id)
                    }
                    Err(e) => Err(format!("创建班级失败: {}", e))
                }
            }
            Err(e) => Err(format!("查找班级代码失败: {}", e)),
        }
    }

    /// 根据班级代码生成班级名称 (901 -> 9年01班)
    fn generate_class_name(&self, class_code: &str) -> Result<String, String> {
        // 支持3位数字格式，如 901, 802 等
        if class_code.len() != 3 {
            return Err(format!("班级代码格式错误，应为3位数字，当前: '{}'", class_code));
        }

        // 验证是否为数字
        if !class_code.chars().all(|c| c.is_ascii_digit()) {
            return Err(format!("班级代码应为纯数字，当前: '{}'", class_code));
        }

        let grade_num = &class_code[0..1];
        let class_num = &class_code[1..3];

        // 移除班级号前导零，如 01 -> 1
        let class_num_int: u32 = class_num.parse()
            .map_err(|_| format!("无法解析班级号: '{}'", class_num))?;

        Ok(format!("{}年{}班", grade_num, class_num_int))
    }

    /// 将年级名称映射到年级代码
    fn map_grade_name_to_code(&self, grade_name: &str) -> Option<String> {
        match grade_name {
            "初一" | "七年级" => Some("G7".to_string()),
            "初二" | "八年级" => Some("G8".to_string()),
            "初三" | "九年级" => Some("G9".to_string()),
            "高一" | "十年级" => Some("G10".to_string()),
            "高二" | "十一年级" => Some("G11".to_string()),
            "高三" | "十二年级" => Some("G12".to_string()),
            "一年级" => Some("G1".to_string()),
            "二年级" => Some("G2".to_string()),
            "三年级" => Some("G3".to_string()),
            "四年级" => Some("G4".to_string()),
            "五年级" => Some("G5".to_string()),
            "六年级" => Some("G6".to_string()),
            _ => None,
        }
    }

    /// 在当前租户下根据学生学号查找，如果存在则返回，不存在才创建
    async fn create_or_get_student(
        &self,
        schema_name: &str,
        record: &StudentImportRecord,
        user_id: Uuid,
        administrative_class_id: Option<Uuid>,
    ) -> Result<Student, String> {
        // 先查找是否已存在该学号的学生
        match self.get_student_by_number(schema_name, &record.student_number).await {
            Ok(Some(mut existing_student)) => {
                info!("Found existing student: {} ({})", existing_student.student_name, existing_student.student_number);

                // 如果学生存在但没有关联用户ID，则更新用户ID
                if existing_student.user_id.is_none() || existing_student.user_id != Some(user_id) {
                    existing_student.user_id = Some(user_id);
                    // 这里可以添加更新学生记录的逻辑
                    info!("Updated student user_id: {} -> {}", existing_student.student_name, user_id);
                }

                Ok(existing_student)
            }
            Ok(None) => {
                // 学生不存在，创建新学生
                let create_params = CreateStudentParams {
                    student_number: record.student_number.clone(),
                    student_name: record.student_name.clone(),
                    gender: None,
                    birth_date: record.birth_date,
                    id_number: record.id_number.clone(),
                    phone: record.phone.clone(),
                    email: record.email.clone(),
                    address: record.address.clone(),
                    guardian_name: record.guardian_name.clone(),
                    guardian_phone: record.guardian_phone.clone(),
                    guardian_relation: record.guardian_relation.clone(),
                    administrative_class_id,
                    user_id: Some(user_id), // 关联用户ID到学生表
                    enrollment_date: None,
                    status: Some("active".to_string()),
                    profile_level: None,
                    profile_tags: None,
                    notes: record.grade_name.clone(),
                };

                self.student_service.create_student(schema_name, &create_params).await
                    .map_err(|e| format!("创建学生失败: {}", e))
            }
            Err(e) => Err(format!("查询学生失败: {}", e))
        }
    }

    /// 根据学号查找学生
    async fn get_student_by_number(&self, schema_name: &str, student_number: &str) -> Result<Option<Student>, String> {
        let query = format!(
            "SELECT * FROM {}.students WHERE student_number = $1 LIMIT 1",
            schema_name
        );

        match sqlx::query_as::<_, Student>(&query)
            .bind(student_number)
            .fetch_optional(&self.db_pool)
            .await
        {
            Ok(student) => Ok(student),
            Err(e) => Err(format!("查询学生失败: {}", e))
        }
    }

    /// 解析Excel行数据
    fn parse_student_row(&self, row: &[calamine::Data], _row_num: usize) -> Result<StudentImportRecord, String> {
        use calamine::Data;
        use chrono::NaiveDate;

        if row.len() < 5 {
            return Err("数据列数不足，至少需要序号、学号、姓名、班级、年级".to_string());
        }

        let student_number = match &row[1] {
            Data::String(s) => s.clone(),
            Data::Int(i) => i.to_string(),
            Data::Float(f) => f.to_string(),
            _ => return Err("学号格式错误".to_string()),
        };

        if student_number.trim().is_empty() {
            return Err("学号不能为空".to_string());
        }

        let student_name = match &row[2] {
            Data::String(s) => s.clone(),
            _ => return Err("姓名格式错误".to_string()),
        };

        if student_name.trim().is_empty() {
            return Err("姓名不能为空".to_string());
        }

        // 解析班级代码 (第3列)
        let class_code = match &row[3] {
            Data::String(s) if !s.trim().is_empty() => Some(s.clone()),
            Data::Int(i) => Some(i.to_string()),
            Data::Float(f) => Some(f.to_string()),
            _ => None,
        };

        // 解析年级名称 (第4列)
        let grade_name = match &row[4] {
            Data::String(s) if !s.trim().is_empty() => Some(s.clone()),
            _ => None,
        };

        // 其他字段设为可选，如果Excel有更多列可以继续解析
        let birth_date = if row.len() > 5 {
            match &row[5] {
                Data::String(s) if !s.trim().is_empty() => {
                    NaiveDate::parse_from_str(s, "%Y-%m-%d")
                        .or_else(|_| NaiveDate::parse_from_str(s, "%Y/%m/%d"))
                        .ok()
                }
                Data::DateTime(dt) => {
                    // Convert ExcelDateTime to chrono::NaiveDate
                    use chrono::NaiveDate;
                    if let Some(excel_epoch) = NaiveDate::from_ymd_opt(1899, 12, 30) {
                        let days = dt.as_f64() as i64;
                        if days >= 0 {
                            excel_epoch.checked_add_days(chrono::Days::new(days as u64))
                        } else {
                            None
                        }
                    } else {
                        None
                    }
                }
                _ => None,
            }
        } else {
            None
        };

        let id_number = if row.len() > 6 {
            match &row[6] {
                Data::String(s) if !s.trim().is_empty() => Some(s.clone()),
                Data::Int(i) => Some(i.to_string()),
                _ => None,
            }
        } else {
            None
        };

        let phone = if row.len() > 7 {
            match &row[7] {
                Data::String(s) if !s.trim().is_empty() => Some(s.clone()),
                Data::Int(i) => Some(i.to_string()),
                _ => None,
            }
        } else {
            None
        };

        let email = if row.len() > 8 {
            match &row[8] {
                Data::String(s) if !s.trim().is_empty() => Some(s.clone()),
                _ => None,
            }
        } else {
            None
        };

        let address = if row.len() > 9 {
            match &row[9] {
                Data::String(s) if !s.trim().is_empty() => Some(s.clone()),
                _ => None,
            }
        } else {
            None
        };

        let guardian_name = if row.len() > 10 {
            match &row[10] {
                Data::String(s) if !s.trim().is_empty() => Some(s.clone()),
                _ => None,
            }
        } else {
            None
        };

        let guardian_phone = if row.len() > 11 {
            match &row[11] {
                Data::String(s) if !s.trim().is_empty() => Some(s.clone()),
                Data::Int(i) => Some(i.to_string()),
                _ => None,
            }
        } else {
            None
        };

        let guardian_relation = if row.len() > 12 {
            match &row[12] {
                Data::String(s) if !s.trim().is_empty() => Some(s.clone()),
                _ => None,
            }
        } else {
            None
        };

        Ok(StudentImportRecord {
            student_number,
            student_name,
            class_code,
            grade_name,
            birth_date,
            id_number,
            phone,
            email,
            address,
            guardian_name,
            guardian_phone,
            guardian_relation,
        })
    }
}
