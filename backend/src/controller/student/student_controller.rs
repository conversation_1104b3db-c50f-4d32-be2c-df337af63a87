use crate::middleware::auth_middleware::AuthExtractor;
use crate::model::{CreateStudentParams, FindAllStudentParams, Student, StudentVo, UpdateStudentParams};
use crate::utils::api_response::{responses, ApiResponse, PaginatedApiResponse};
use crate::web_server::AppState;
use axum::http::HeaderMap;
use axum::{
    extract::{Path, State, Multipart},
    routing::post,
    Json, Router,
};
use tracing::{info, error, warn, debug};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/pageAllStudent", post(page_all_student))
        .route("/createStudent", post(create_student))
        .route("/updateStudent", post(update_student))
        .route("/importStudents", post(import_students))
}

pub async fn page_all_student(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    <PERSON><PERSON>(params): Json<FindAllStudentParams>,
) -> Result<PaginatedApiResponse<StudentVo>, PaginatedApiResponse<()>> {
    debug!("User roles: {:?}", context.roles);
    info!("User ID: {}, Tenant: {}", context.user_id, tenant_name);

    // Casbin permission check
    // let mut has_permission = false;
    //
    // if context.roles.is_empty() {
    //     info!("No roles found for user: {}", context.user_id);
    //     return Err(responses::paginated_error("用户无角色信息！", None));
    // }
    //
    // for role in &context.roles {
    //     let req = PermissionRequest {
    //         subject: role.identity_type.clone(),
    //         domain: tenant_name.clone(),
    //         object: "student".to_string(),
    //         action: "read".to_string(),
    //     };
    //
    //     info!("Checking permission for role: {} with request: {:?}", role.identity_type, req);
    //
    //     match state.casbin_service.enforce(&req).await {
    //         Ok(result) => {
    //             info!("Permission check result for role {}: {}", role.identity_type, result);
    //             if result {
    //                 has_permission = true;
    //                 info!("Permission granted for role: {}", role.identity_type);
    //                 break;
    //             }
    //         },
    //         Err(e) => {
    //             info!("Permission check error for role {}: {}", role.identity_type, e);
    //         }
    //     }
    // }
    //
    // if !has_permission {
    //     info!("No permission found for user {} in tenant {}", context.user_id, tenant_name);
    //     return Err(responses::paginated_error("权限不足！", None));
    // }

    info!("Permission granted, proceeding with student query");
    let tenant_id = context.get_tenant_id_from_headers(header_map);

    // 检查用户权限：管理员可以查看所有学生，班主任只能查看自己负责班级的学生
    let user_id_for_filter = if context.is_admin_in_tenant(tenant_id) {
        // 管理员不需要过滤，传递None表示查看所有学生
        None
    } else {
        // 其他有权限的角色，需要根据其ID进行权限过滤
        Some(context.user_id)
    };

    state
        .student_service
        .page_all_student(&tenant_name, &params, user_id_for_filter, Some(tenant_name.clone()), Some(state.casbin_service.as_ref()))
        .await
        .map_err(|e| responses::paginated_error(&e, None))
        .map(|(list, total)| {
            responses::paginated_success(
                list,
                params.page_params.get_page(),
                params.page_params.get_page_size(),
                total,
                None,
            )
        })
}

pub async fn create_student(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<CreateStudentParams>,
) -> Result<ApiResponse<Student>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }
    state
        .student_service
        .create_student(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn update_student(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<UpdateStudentParams>,
) -> Result<ApiResponse<Student>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }
    state
        .student_service
        .update_student(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportResult {
    pub success: i32,
    pub failed: i32,
    pub errors: Vec<ImportError>,
    pub sheets_processed: Vec<SheetResult>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SheetResult {
    pub sheet_name: String,
    pub success: i32,
    pub failed: i32,
    pub errors: Vec<ImportError>,
}

#[derive(Debug, Serialize, Deserialize,Clone)]
pub struct ImportError {
    pub row: i32,
    pub error: String,
    pub data: serde_json::Value,
}

pub async fn import_students(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    mut multipart: Multipart,
) -> Result<ApiResponse<ImportResult>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }

    // 处理上传的文件
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        error!("Failed to get multipart field: {}", e);
        responses::error("文件上传失败", None)
    })? {
        if field.name() == Some("file") {
            let file_name = field.file_name().unwrap_or("unknown").to_string();
            info!("Processing uploaded file: {}", file_name);

            // 检查文件类型
            if !file_name.ends_with(".xlsx") && !file_name.ends_with(".xls") {
                return Err(responses::error("仅支持Excel文件格式(.xlsx, .xls)", None));
            }

            let data = field.bytes().await.map_err(|e| {
                error!("Failed to read file bytes: {}", e);
                responses::error("读取文件失败", None)
            })?;

            // 使用StudentImportService处理Excel文件并导入学生数据
            match state.student_import_service.import_students(&tenant_name, &data, context.user_id).await {
                Ok(result) => return Ok(responses::success(result, None)),
                Err(e) => return Err(responses::error(&e, None)),
            }
        }
    }

    Err(responses::error("未找到上传文件", None))
}
